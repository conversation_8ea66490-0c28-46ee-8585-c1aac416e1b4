#!/bin/bash

# 停止开发服务器脚本

echo "🛑 停止 Laravel + Nuxt 开发服务器..."

# 停止 Laravel 服务器
if [ -f "logs/laravel.pid" ]; then
    LARAVEL_PID=$(cat logs/laravel.pid)
    if kill -0 $LARAVEL_PID 2>/dev/null; then
        kill $LARAVEL_PID
        echo "✅ Laravel 服务器已停止"
    else
        echo "⚠️  Laravel 服务器进程不存在"
    fi
    rm -f logs/laravel.pid
else
    echo "⚠️  未找到 Laravel 进程 ID 文件"
fi

# 停止 Nuxt 服务器
if [ -f "logs/nuxt.pid" ]; then
    NUXT_PID=$(cat logs/nuxt.pid)
    if kill -0 $NUXT_PID 2>/dev/null; then
        kill $NUXT_PID
        echo "✅ Nuxt 服务器已停止"
    else
        echo "⚠️  Nuxt 服务器进程不存在"
    fi
    rm -f logs/nuxt.pid
else
    echo "⚠️  未找到 Nuxt 进程 ID 文件"
fi

# 清理可能残留的进程
echo "🧹 清理残留进程..."

# 查找并停止可能的 Laravel 进程
LARAVEL_PROCESSES=$(ps aux | grep "php artisan serve" | grep -v grep | awk '{print $2}')
if [ ! -z "$LARAVEL_PROCESSES" ]; then
    echo "发现残留的 Laravel 进程，正在停止..."
    echo $LARAVEL_PROCESSES | xargs kill 2>/dev/null || true
fi

# 查找并停止可能的 Nuxt 进程
NUXT_PROCESSES=$(ps aux | grep "nuxt.*dev" | grep -v grep | awk '{print $2}')
if [ ! -z "$NUXT_PROCESSES" ]; then
    echo "发现残留的 Nuxt 进程，正在停止..."
    echo $NUXT_PROCESSES | xargs kill 2>/dev/null || true
fi

echo "👋 所有开发服务器已停止"
