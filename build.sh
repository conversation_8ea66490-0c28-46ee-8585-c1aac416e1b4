#!/bin/bash

# Laravel + Nuxt 渐进式增强项目构建脚本

set -e

echo "🚀 开始构建 Laravel + Nuxt 渐进式增强项目..."

# 检查环境
check_environment() {
    echo "📋 检查构建环境..."
    
    # 检查 PHP
    if ! command -v php &> /dev/null; then
        echo "❌ PHP 未安装"
        exit 1
    fi
    
    # 检查 Composer
    if ! command -v composer &> /dev/null; then
        echo "❌ Composer 未安装"
        exit 1
    fi
    
    # 检查 Node.js
    if ! command -v node &> /dev/null; then
        echo "❌ Node.js 未安装"
        exit 1
    fi
    
    # 检查 npm
    if ! command -v npm &> /dev/null; then
        echo "❌ npm 未安装"
        exit 1
    fi
    
    echo "✅ 环境检查通过"
}

# 安装 Laravel 依赖
install_laravel_dependencies() {
    echo "📦 安装 Laravel 依赖..."
    composer install --no-dev --optimize-autoloader
    echo "✅ Laravel 依赖安装完成"
}

# 构建 Nuxt 项目
build_nuxt() {
    echo "🔨 构建 Nuxt 前端项目..."
    
    cd frontend
    
    # 安装依赖
    echo "📦 安装 Nuxt 依赖..."
    npm ci
    
    # 构建项目
    echo "🔨 构建 Nuxt 应用..."
    npm run build
    
    # 生成静态文件
    echo "📄 生成静态文件..."
    npm run generate
    
    cd ..
    
    echo "✅ Nuxt 构建完成"
}

# 优化 Laravel
optimize_laravel() {
    echo "⚡ 优化 Laravel..."
    
    # 清除缓存
    php artisan cache:clear
    php artisan config:clear
    php artisan route:clear
    php artisan view:clear
    
    # 生成优化缓存
    php artisan config:cache
    php artisan route:cache
    php artisan view:cache
    
    echo "✅ Laravel 优化完成"
}

# 设置权限
set_permissions() {
    echo "🔐 设置文件权限..."
    
    # 设置存储目录权限
    chmod -R 775 storage
    chmod -R 775 bootstrap/cache
    
    # 设置公共目录权限
    chmod -R 755 public
    
    echo "✅ 权限设置完成"
}

# 复制 Nuxt 构建文件到 Laravel public 目录
copy_nuxt_assets() {
    echo "📁 复制 Nuxt 资源文件..."
    
    # 创建目标目录
    mkdir -p public/nuxt
    
    # 复制 Nuxt 构建输出
    if [ -d "frontend/.output/public" ]; then
        cp -r frontend/.output/public/* public/nuxt/
        echo "✅ Nuxt 资源文件复制完成"
    else
        echo "⚠️  Nuxt 构建输出目录不存在，跳过资源复制"
    fi
}

# 创建部署信息文件
create_deployment_info() {
    echo "📝 创建部署信息..."
    
    cat > public/deployment-info.json << EOF
{
    "timestamp": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
    "version": "1.0.0",
    "laravel_version": "$(php artisan --version | cut -d' ' -f3)",
    "php_version": "$(php -v | head -n1 | cut -d' ' -f2)",
    "node_version": "$(node -v)",
    "build_environment": "${BUILD_ENV:-development}",
    "git_commit": "$(git rev-parse --short HEAD 2>/dev/null || echo 'unknown')"
}
EOF
    
    echo "✅ 部署信息创建完成"
}

# 主构建流程
main() {
    echo "🎯 开始主构建流程..."
    
    check_environment
    install_laravel_dependencies
    build_nuxt
    copy_nuxt_assets
    optimize_laravel
    set_permissions
    create_deployment_info
    
    echo ""
    echo "🎉 构建完成！"
    echo ""
    echo "📋 构建摘要:"
    echo "   - Laravel 后端: ✅ 已优化"
    echo "   - Nuxt 前端: ✅ 已构建"
    echo "   - 资源文件: ✅ 已复制"
    echo "   - 权限设置: ✅ 已配置"
    echo ""
    echo "🚀 项目已准备好部署！"
    echo ""
    echo "📖 下一步:"
    echo "   1. 配置 Web 服务器指向 public 目录"
    echo "   2. 设置环境变量 (.env 文件)"
    echo "   3. 运行数据库迁移: php artisan migrate"
    echo "   4. 启动应用: php artisan serve"
}

# 错误处理
trap 'echo "❌ 构建失败！请检查上面的错误信息。"; exit 1' ERR

# 运行主流程
main "$@"
