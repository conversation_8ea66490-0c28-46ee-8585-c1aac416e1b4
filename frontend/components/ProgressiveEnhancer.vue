<template>
  <div class="progressive-enhancer">
    <!-- 这个组件负责扫描页面并增强静态内容 -->
  </div>
</template>

<script setup lang="ts">
// 渐进式增强器 - 扫描页面中的静态内容并用Vue组件增强

onMounted(() => {
  enhanceStaticContent()
})

const enhanceStaticContent = () => {
  // 增强文章预览组件
  const articlePreviews = document.querySelectorAll('[data-nuxt-component="article-preview"]')
  articlePreviews.forEach(element => {
    const articleId = element.getAttribute('data-article-id')
    if (articleId) {
      enhanceArticlePreview(element as HTMLElement, articleId)
    }
  })

  // 增强文章交互组件
  const articleInteractions = document.querySelectorAll('[data-nuxt-component="article-interactions"]')
  articleInteractions.forEach(element => {
    const articleId = element.getAttribute('data-article-id')
    if (articleId) {
      enhanceArticleInteractions(element as HTMLElement, articleId)
    }
  })

  // 增强评论组件
  const articleComments = document.querySelectorAll('[data-nuxt-component="article-comments"]')
  articleComments.forEach(element => {
    const articleId = element.getAttribute('data-article-id')
    if (articleId) {
      enhanceArticleComments(element as HTMLElement, articleId)
    }
  })
}

const enhanceArticlePreview = (element: HTMLElement, articleId: string) => {
  // 检查是否已经被增强过
  if (element.classList.contains('vue-enhanced')) return

  // 获取初始状态
  const likedArticles = JSON.parse(localStorage.getItem('likedArticles') || '[]')
  const initialLiked = likedArticles.includes(articleId)

  // 创建Vue组件实例
  const { createApp } = await import('vue')
  const ArticlePreview = await import('./ArticlePreview.vue')
  
  const app = createApp(ArticlePreview.default, {
    articleId,
    initialLiked,
    initialLikeCount: 0
  })

  // 挂载到元素
  const wrapper = document.createElement('div')
  element.appendChild(wrapper)
  app.mount(wrapper)

  // 标记为已增强
  element.classList.add('vue-enhanced')
  
  console.log(`Enhanced article preview for article ${articleId}`)
}

const enhanceArticleInteractions = (element: HTMLElement, articleId: string) => {
  // 检查是否已经被增强过
  if (element.classList.contains('vue-enhanced')) return

  // 获取初始状态
  const likedArticles = JSON.parse(localStorage.getItem('likedArticles') || '[]')
  const initialLiked = likedArticles.includes(articleId)

  // 创建Vue组件实例
  const { createApp } = await import('vue')
  const ArticleInteractions = await import('./ArticleInteractions.vue')
  
  const app = createApp(ArticleInteractions.default, {
    articleId,
    initialLiked
  })

  // 挂载到元素
  const wrapper = document.createElement('div')
  element.appendChild(wrapper)
  app.mount(wrapper)

  // 标记为已增强
  element.classList.add('vue-enhanced')
  
  console.log(`Enhanced article interactions for article ${articleId}`)
}

const enhanceArticleComments = (element: HTMLElement, articleId: string) => {
  // 检查是否已经被增强过
  if (element.classList.contains('vue-enhanced')) return

  // 创建Vue组件实例
  const { createApp } = await import('vue')
  const ArticleComments = await import('./ArticleComments.vue')
  
  const app = createApp(ArticleComments.default, {
    articleId
  })

  // 替换静态内容
  element.innerHTML = ''
  app.mount(element)

  // 标记为已增强
  element.classList.add('vue-enhanced')
  
  console.log(`Enhanced article comments for article ${articleId}`)
}

// 监听页面变化（用于SPA导航）
const observePageChanges = () => {
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'childList') {
        // 检查是否有新的需要增强的元素
        const addedNodes = Array.from(mutation.addedNodes)
        addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            const element = node as HTMLElement
            
            // 检查新添加的元素是否需要增强
            if (element.hasAttribute('data-nuxt-component')) {
              setTimeout(() => enhanceStaticContent(), 100)
            }
            
            // 检查子元素
            const componentsToEnhance = element.querySelectorAll('[data-nuxt-component]')
            if (componentsToEnhance.length > 0) {
              setTimeout(() => enhanceStaticContent(), 100)
            }
          }
        })
      }
    })
  })

  observer.observe(document.body, {
    childList: true,
    subtree: true
  })

  return observer
}

// 启动页面变化监听
onMounted(() => {
  const observer = observePageChanges()
  
  onUnmounted(() => {
    observer.disconnect()
  })
})

// 全局错误处理
const handleEnhancementError = (error: Error, componentType: string, articleId: string) => {
  console.error(`Failed to enhance ${componentType} for article ${articleId}:`, error)
  
  // 可以在这里添加错误报告逻辑
  // 例如发送到错误监控服务
}

// 导出增强函数供外部使用
defineExpose({
  enhanceStaticContent,
  enhanceArticlePreview,
  enhanceArticleInteractions,
  enhanceArticleComments
})
</script>

<style scoped>
.progressive-enhancer {
  /* 隐藏增强器本身 */
  display: none;
}
</style>
