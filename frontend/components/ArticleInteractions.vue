<template>
  <div class="vue-enhanced progressive-enhancement">
    <!-- 文章交互区域 -->
    <div class="flex items-center justify-between mb-6">
      <!-- 点赞和分享 -->
      <div class="flex items-center space-x-4">
        <button
          @click="toggleLike"
          :disabled="isLoading"
          class="btn-enhanced inline-flex items-center px-4 py-2 rounded-lg transition-all duration-200"
          :class="[
            isLiked 
              ? 'bg-red-500 text-white hover:bg-red-600 shadow-lg' 
              : 'bg-red-100 text-red-700 hover:bg-red-200',
            isLoading ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
          ]"
        >
          <svg 
            class="w-5 h-5 mr-2 transition-transform duration-200" 
            :class="{ 'scale-110': isLiked, 'animate-pulse': isLoading }"
            :fill="isLiked ? 'currentColor' : 'none'" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path 
              stroke-linecap="round" 
              stroke-linejoin="round" 
              stroke-width="2" 
              d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
            />
          </svg>
          <span v-if="isLoading">处理中...</span>
          <span v-else>{{ isLiked ? '已点赞这篇文章' : '点赞这篇文章' }}</span>
        </button>

        <!-- 分享按钮组 -->
        <div class="flex items-center space-x-2">
          <span class="text-gray-600 text-sm">分享:</span>
          <button
            @click="shareToTwitter"
            class="btn-enhanced p-2 text-blue-500 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-colors"
            title="分享到Twitter"
          >
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
            </svg>
          </button>
          <button
            @click="shareToFacebook"
            class="btn-enhanced p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-colors"
            title="分享到Facebook"
          >
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
            </svg>
          </button>
          <button
            @click="copyLink"
            class="btn-enhanced p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-50 rounded-lg transition-colors"
            :title="linkCopied ? '已复制!' : '复制链接'"
          >
            <svg v-if="!linkCopied" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"/>
            </svg>
            <svg v-else class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- 反馈消息 -->
    <Transition name="fade">
      <div v-if="feedbackMessage" class="mb-4 p-3 rounded-lg" :class="feedbackClass">
        {{ feedbackMessage }}
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
interface Props {
  articleId: string | number
  initialLiked?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  initialLiked: false
})

// 响应式状态
const isLoading = ref(false)
const isLiked = ref(props.initialLiked)
const linkCopied = ref(false)
const feedbackMessage = ref('')
const feedbackClass = ref('')

// 运行时配置
const config = useRuntimeConfig()

// 点赞功能
const toggleLike = async () => {
  if (isLoading.value) return
  
  isLoading.value = true
  
  try {
    const response = await $fetch(`${config.public.apiBase}/articles/${props.articleId}/like`, {
      method: 'POST',
      headers: {
        'X-Requested-With': 'XMLHttpRequest',
        'Content-Type': 'application/json'
      }
    })
    
    isLiked.value = response.liked
    showFeedback(response.message, 'success')
  } catch (error) {
    console.error('点赞失败:', error)
    showFeedback('操作失败，请稍后重试', 'error')
  } finally {
    isLoading.value = false
  }
}

// 分享功能
const shareToTwitter = () => {
  const url = encodeURIComponent(window.location.href)
  const text = encodeURIComponent(document.title)
  window.open(`https://twitter.com/intent/tweet?text=${text}&url=${url}`, '_blank')
  showFeedback('已打开Twitter分享', 'success')
}

const shareToFacebook = () => {
  const url = encodeURIComponent(window.location.href)
  window.open(`https://www.facebook.com/sharer/sharer.php?u=${url}`, '_blank')
  showFeedback('已打开Facebook分享', 'success')
}

const copyLink = async () => {
  try {
    await navigator.clipboard.writeText(window.location.href)
    linkCopied.value = true
    setTimeout(() => {
      linkCopied.value = false
    }, 2000)
    showFeedback('链接已复制到剪贴板', 'success')
  } catch (error) {
    showFeedback('复制失败，请手动复制链接', 'error')
  }
}

// 反馈消息
const showFeedback = (message: string, type: 'success' | 'error') => {
  feedbackMessage.value = message
  feedbackClass.value = type === 'success' 
    ? 'bg-green-100 text-green-800 border border-green-200' 
    : 'bg-red-100 text-red-800 border border-red-200'
  
  setTimeout(() => {
    feedbackMessage.value = ''
  }, 3000)
}

// 检查用户是否已经点赞过
onMounted(async () => {
  // 这里可以添加检查用户点赞状态的逻辑
  // 例如从localStorage或API获取状态
  const likedArticles = JSON.parse(localStorage.getItem('likedArticles') || '[]')
  isLiked.value = likedArticles.includes(props.articleId.toString())
})
</script>

<style scoped>
.btn-enhanced:active {
  transform: scale(0.95);
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
</style>
