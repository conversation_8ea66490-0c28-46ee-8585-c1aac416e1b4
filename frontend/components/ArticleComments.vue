<template>
  <div class="vue-enhanced progressive-enhancement">
    <h3 class="text-lg font-semibold text-gray-900 mb-4">评论 ({{ comments.length }})</h3>
    
    <!-- 评论表单 -->
    <div class="mb-6 p-4 bg-gray-50 rounded-lg">
      <form @submit.prevent="submitComment" class="space-y-4">
        <div>
          <label for="comment-name" class="block text-sm font-medium text-gray-700 mb-1">
            姓名 *
          </label>
          <input
            id="comment-name"
            v-model="newComment.name"
            type="text"
            required
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="请输入您的姓名"
          />
        </div>
        
        <div>
          <label for="comment-email" class="block text-sm font-medium text-gray-700 mb-1">
            邮箱 (可选)
          </label>
          <input
            id="comment-email"
            v-model="newComment.email"
            type="email"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="<EMAIL>"
          />
        </div>
        
        <div>
          <label for="comment-content" class="block text-sm font-medium text-gray-700 mb-1">
            评论内容 *
          </label>
          <textarea
            id="comment-content"
            v-model="newComment.content"
            required
            rows="4"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-vertical"
            placeholder="分享您的想法..."
          ></textarea>
        </div>
        
        <div class="flex items-center justify-between">
          <div class="text-sm text-gray-500">
            <span class="flex items-center">
              <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
              评论将在审核后显示
            </span>
          </div>
          <button
            type="submit"
            :disabled="isSubmitting || !newComment.name.trim() || !newComment.content.trim()"
            class="btn-enhanced px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <span v-if="isSubmitting" class="flex items-center">
              <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              提交中...
            </span>
            <span v-else>发表评论</span>
          </button>
        </div>
      </form>
    </div>

    <!-- 反馈消息 -->
    <Transition name="fade">
      <div v-if="feedbackMessage" class="mb-4 p-3 rounded-lg" :class="feedbackClass">
        {{ feedbackMessage }}
      </div>
    </Transition>

    <!-- 评论列表 -->
    <div class="space-y-4">
      <div v-if="comments.length === 0" class="text-center py-8 text-gray-500">
        <svg class="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>
        </svg>
        <p>还没有评论，来发表第一个评论吧！</p>
      </div>
      
      <TransitionGroup name="comment" tag="div">
        <div
          v-for="comment in comments"
          :key="comment.id"
          class="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-sm transition-shadow"
        >
          <div class="flex items-start space-x-3">
            <!-- 头像 -->
            <div class="flex-shrink-0">
              <div class="w-10 h-10 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center text-white font-semibold">
                {{ comment.name.charAt(0).toUpperCase() }}
              </div>
            </div>
            
            <!-- 评论内容 -->
            <div class="flex-1 min-w-0">
              <div class="flex items-center space-x-2 mb-1">
                <h4 class="text-sm font-semibold text-gray-900">{{ comment.name }}</h4>
                <span class="text-xs text-gray-500">{{ formatDate(comment.created_at) }}</span>
              </div>
              <p class="text-gray-700 text-sm leading-relaxed">{{ comment.content }}</p>
              
              <!-- 评论操作 -->
              <div class="mt-2 flex items-center space-x-4 text-xs text-gray-500">
                <button
                  @click="likeComment(comment.id)"
                  class="hover:text-blue-600 transition-colors flex items-center space-x-1"
                >
                  <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5"/>
                  </svg>
                  <span>赞 ({{ comment.likes || 0 }})</span>
                </button>
                <button class="hover:text-blue-600 transition-colors">回复</button>
              </div>
            </div>
          </div>
        </div>
      </TransitionGroup>
    </div>

    <!-- 加载更多 -->
    <div v-if="hasMoreComments" class="mt-6 text-center">
      <button
        @click="loadMoreComments"
        :disabled="isLoadingMore"
        class="btn-enhanced px-4 py-2 text-blue-600 border border-blue-600 rounded-md hover:bg-blue-50 disabled:opacity-50 transition-colors"
      >
        <span v-if="isLoadingMore">加载中...</span>
        <span v-else>加载更多评论</span>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Comment {
  id: number
  name: string
  email?: string
  content: string
  created_at: string
  likes?: number
}

interface Props {
  articleId: string | number
}

const props = defineProps<Props>()

// 响应式状态
const comments = ref<Comment[]>([])
const newComment = ref({
  name: '',
  email: '',
  content: ''
})
const isSubmitting = ref(false)
const isLoadingMore = ref(false)
const hasMoreComments = ref(false)
const feedbackMessage = ref('')
const feedbackClass = ref('')

// 运行时配置
const config = useRuntimeConfig()

// 提交评论
const submitComment = async () => {
  if (isSubmitting.value) return
  
  isSubmitting.value = true
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const comment: Comment = {
      id: Date.now(),
      name: newComment.value.name,
      email: newComment.value.email,
      content: newComment.value.content,
      created_at: new Date().toISOString(),
      likes: 0
    }
    
    comments.value.unshift(comment)
    
    // 重置表单
    newComment.value = {
      name: '',
      email: '',
      content: ''
    }
    
    showFeedback('评论提交成功，感谢您的参与！', 'success')
  } catch (error) {
    console.error('提交评论失败:', error)
    showFeedback('提交失败，请稍后重试', 'error')
  } finally {
    isSubmitting.value = false
  }
}

// 点赞评论
const likeComment = async (commentId: number) => {
  const comment = comments.value.find(c => c.id === commentId)
  if (comment) {
    comment.likes = (comment.likes || 0) + 1
    showFeedback('已点赞该评论', 'success')
  }
}

// 加载更多评论
const loadMoreComments = async () => {
  isLoadingMore.value = true
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟没有更多评论
    hasMoreComments.value = false
    showFeedback('已加载所有评论', 'success')
  } catch (error) {
    showFeedback('加载失败，请稍后重试', 'error')
  } finally {
    isLoadingMore.value = false
  }
}

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  const minutes = Math.floor(diff / 60000)
  const hours = Math.floor(diff / 3600000)
  const days = Math.floor(diff / 86400000)
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  if (days < 7) return `${days}天前`
  
  return date.toLocaleDateString('zh-CN')
}

// 反馈消息
const showFeedback = (message: string, type: 'success' | 'error') => {
  feedbackMessage.value = message
  feedbackClass.value = type === 'success' 
    ? 'bg-green-100 text-green-800 border border-green-200' 
    : 'bg-red-100 text-red-800 border border-red-200'
  
  setTimeout(() => {
    feedbackMessage.value = ''
  }, 3000)
}

// 初始化
onMounted(() => {
  // 模拟初始评论数据
  comments.value = [
    {
      id: 1,
      name: '张三',
      content: '这篇文章写得很好，对渐进式增强的概念解释得很清楚！',
      created_at: new Date(Date.now() - 3600000).toISOString(),
      likes: 5
    },
    {
      id: 2,
      name: '李四',
      content: 'Laravel + Nuxt 的组合确实很强大，期待看到更多实际应用案例。',
      created_at: new Date(Date.now() - 7200000).toISOString(),
      likes: 3
    }
  ]
  
  hasMoreComments.value = true
})
</script>

<style scoped>
.comment-enter-active,
.comment-leave-active {
  transition: all 0.3s ease;
}

.comment-enter-from {
  opacity: 0;
  transform: translateY(-10px);
}

.comment-leave-to {
  opacity: 0;
  transform: translateX(10px);
}

.comment-move {
  transition: transform 0.3s ease;
}
</style>
