<template>
  <div class="vue-enhanced progressive-enhancement" :class="{ loading: isLoading }">
    <!-- 点赞按钮增强版 -->
    <div class="flex items-center space-x-4">
      <button
        @click="toggleLike"
        :disabled="isLoading"
        class="btn-enhanced inline-flex items-center px-3 py-1 text-sm rounded-md transition-all duration-200"
        :class="[
          isLiked 
            ? 'bg-red-500 text-white hover:bg-red-600' 
            : 'bg-gray-100 text-gray-700 hover:bg-gray-200',
          isLoading ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
        ]"
      >
        <svg 
          class="w-4 h-4 mr-1 transition-transform duration-200" 
          :class="{ 'scale-110': isLiked }"
          :fill="isLiked ? 'currentColor' : 'none'" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
        >
          <path 
            stroke-linecap="round" 
            stroke-linejoin="round" 
            stroke-width="2" 
            d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
          />
        </svg>
        <span v-if="isLoading">处理中...</span>
        <span v-else>{{ isLiked ? '已点赞' : '点赞' }}</span>
        <span v-if="likeCount > 0" class="ml-1 text-xs">({{ likeCount }})</span>
      </button>

      <!-- 分享按钮 -->
      <button
        @click="showShareMenu = !showShareMenu"
        class="btn-enhanced inline-flex items-center px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-colors"
      >
        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
        </svg>
        分享
      </button>
    </div>

    <!-- 分享菜单 -->
    <Transition name="fade">
      <div v-if="showShareMenu" class="mt-2 p-3 bg-white border rounded-lg shadow-lg">
        <div class="flex space-x-2">
          <button
            @click="shareToTwitter"
            class="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
          >
            Twitter
          </button>
          <button
            @click="shareToFacebook"
            class="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
          >
            Facebook
          </button>
          <button
            @click="copyLink"
            class="px-3 py-1 text-sm bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
          >
            {{ linkCopied ? '已复制!' : '复制链接' }}
          </button>
        </div>
      </div>
    </Transition>

    <!-- 实时反馈 -->
    <Transition name="fade">
      <div v-if="feedbackMessage" class="mt-2 p-2 text-sm rounded" :class="feedbackClass">
        {{ feedbackMessage }}
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
interface Props {
  articleId: string | number
  initialLiked?: boolean
  initialLikeCount?: number
}

const props = withDefaults(defineProps<Props>(), {
  initialLiked: false,
  initialLikeCount: 0
})

// 响应式状态
const isLoading = ref(false)
const isLiked = ref(props.initialLiked)
const likeCount = ref(props.initialLikeCount)
const showShareMenu = ref(false)
const linkCopied = ref(false)
const feedbackMessage = ref('')
const feedbackClass = ref('')

// 运行时配置
const config = useRuntimeConfig()

// 点赞功能
const toggleLike = async () => {
  if (isLoading.value) return
  
  isLoading.value = true
  
  try {
    const response = await $fetch(`${config.public.apiBase}/articles/${props.articleId}/like`, {
      method: 'POST',
      headers: {
        'X-Requested-With': 'XMLHttpRequest',
        'Content-Type': 'application/json'
      }
    })
    
    isLiked.value = response.liked
    if (response.liked && !props.initialLiked) {
      likeCount.value += 1
    }
    
    showFeedback(response.message, 'success')
  } catch (error) {
    console.error('点赞失败:', error)
    showFeedback('操作失败，请稍后重试', 'error')
  } finally {
    isLoading.value = false
  }
}

// 分享功能
const shareToTwitter = () => {
  const url = encodeURIComponent(window.location.href)
  const text = encodeURIComponent('查看这篇精彩文章')
  window.open(`https://twitter.com/intent/tweet?text=${text}&url=${url}`, '_blank')
  showShareMenu.value = false
}

const shareToFacebook = () => {
  const url = encodeURIComponent(window.location.href)
  window.open(`https://www.facebook.com/sharer/sharer.php?u=${url}`, '_blank')
  showShareMenu.value = false
}

const copyLink = async () => {
  try {
    await navigator.clipboard.writeText(window.location.href)
    linkCopied.value = true
    setTimeout(() => {
      linkCopied.value = false
    }, 2000)
    showFeedback('链接已复制到剪贴板', 'success')
  } catch (error) {
    showFeedback('复制失败', 'error')
  }
  showShareMenu.value = false
}

// 反馈消息
const showFeedback = (message: string, type: 'success' | 'error') => {
  feedbackMessage.value = message
  feedbackClass.value = type === 'success' 
    ? 'bg-green-100 text-green-800 border border-green-200' 
    : 'bg-red-100 text-red-800 border border-red-200'
  
  setTimeout(() => {
    feedbackMessage.value = ''
  }, 3000)
}

// 点击外部关闭分享菜单
onMounted(() => {
  const handleClickOutside = (event: Event) => {
    if (showShareMenu.value && !(event.target as Element).closest('.vue-enhanced')) {
      showShareMenu.value = false
    }
  }
  
  document.addEventListener('click', handleClickOutside)
  
  onUnmounted(() => {
    document.removeEventListener('click', handleClickOutside)
  })
})
</script>

<style scoped>
/* 组件特定样式 */
.btn-enhanced:active {
  transform: scale(0.98);
}
</style>
