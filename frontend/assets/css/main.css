@tailwind base;
@tailwind components;
@tailwind utilities;

/* 渐进式增强的基础样式 */
.progressive-enhancement {
  /* 确保在JavaScript加载前内容可见 */
  opacity: 1;
  transition: opacity 0.3s ease;
}

.progressive-enhancement.loading {
  opacity: 0.7;
}

/* Vue组件增强样式 */
.vue-enhanced {
  /* 标记已被Vue接管的元素 */
  position: relative;
}

.vue-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  border: 2px solid transparent;
  transition: border-color 0.2s ease;
}

.vue-enhanced:hover::before {
  border-color: rgba(59, 130, 246, 0.3);
}

/* 动画效果 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 按钮增强样式 */
.btn-enhanced {
  position: relative;
  overflow: hidden;
}

.btn-enhanced::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.3s ease, height 0.3s ease;
}

.btn-enhanced:active::after {
  width: 300px;
  height: 300px;
}
