// 客户端渐进式增强插件
export default defineNuxtPlugin(() => {
  // 只在客户端运行
  if (process.server) return

  console.log('Progressive Enhancement Plugin loaded')

  // 等待DOM完全加载
  const initializeEnhancement = () => {
    // 检查是否有Laravel传递的配置
    const appConfig = (window as any).APP_CONFIG
    if (appConfig) {
      console.log('Laravel config detected:', appConfig)
    }

    // 检查页面类型
    const pageType = (window as any).PAGE_TYPE
    if (pageType) {
      console.log('Page type:', pageType)
      
      // 根据页面类型执行不同的增强逻辑
      switch (pageType) {
        case 'articles.index':
          enhanceArticleIndex()
          break
        case 'articles.show':
          enhanceArticleShow()
          break
        default:
          enhanceGeneral()
      }
    }
  }

  const enhanceArticleIndex = () => {
    console.log('Enhancing article index page')
    
    // 获取文章数据
    const articlesData = (window as any).ARTICLES_DATA
    if (articlesData) {
      console.log('Articles data available:', articlesData)
    }

    // 增强文章预览卡片
    const articlePreviews = document.querySelectorAll('[data-nuxt-component="article-preview"]')
    console.log(`Found ${articlePreviews.length} article previews to enhance`)
  }

  const enhanceArticleShow = () => {
    console.log('Enhancing article show page')
    
    // 获取文章数据
    const articleData = (window as any).ARTICLE_DATA
    if (articleData) {
      console.log('Article data available:', articleData)
    }

    // 增强文章交互
    const interactions = document.querySelectorAll('[data-nuxt-component="article-interactions"]')
    const comments = document.querySelectorAll('[data-nuxt-component="article-comments"]')
    
    console.log(`Found ${interactions.length} interaction components and ${comments.length} comment components to enhance`)
  }

  const enhanceGeneral = () => {
    console.log('Enhancing general page elements')
    
    // 通用增强逻辑
    const allComponents = document.querySelectorAll('[data-nuxt-component]')
    console.log(`Found ${allComponents.length} components to enhance`)
  }

  // 添加全局样式类来标识JavaScript已加载
  const addJavaScriptLoadedClass = () => {
    document.documentElement.classList.add('js-loaded')
    document.documentElement.classList.remove('no-js')
  }

  // 设置CSRF令牌
  const setupCSRFToken = () => {
    const appConfig = (window as any).APP_CONFIG
    if (appConfig && appConfig.csrfToken) {
      // 设置默认的CSRF令牌
      const metaTag = document.querySelector('meta[name="csrf-token"]')
      if (metaTag) {
        metaTag.setAttribute('content', appConfig.csrfToken)
      }
    }
  }

  // 初始化性能监控
  const initPerformanceMonitoring = () => {
    // 监控组件增强性能
    if ('performance' in window && 'mark' in performance) {
      performance.mark('nuxt-enhancement-start')
    }
  }

  // 错误处理
  const setupErrorHandling = () => {
    window.addEventListener('error', (event) => {
      console.error('Progressive Enhancement Error:', event.error)
      // 这里可以添加错误报告逻辑
    })

    window.addEventListener('unhandledrejection', (event) => {
      console.error('Progressive Enhancement Promise Rejection:', event.reason)
      // 这里可以添加错误报告逻辑
    })
  }

  // 主初始化函数
  const initialize = () => {
    addJavaScriptLoadedClass()
    setupCSRFToken()
    initPerformanceMonitoring()
    setupErrorHandling()
    
    // 延迟执行增强逻辑，确保DOM完全准备好
    setTimeout(() => {
      initializeEnhancement()
      
      // 标记增强完成
      if ('performance' in window && 'mark' in performance) {
        performance.mark('nuxt-enhancement-end')
        performance.measure('nuxt-enhancement-duration', 'nuxt-enhancement-start', 'nuxt-enhancement-end')
      }
      
      // 触发自定义事件
      window.dispatchEvent(new CustomEvent('nuxt:enhancement:complete'))
    }, 100)
  }

  // 检查DOM状态并初始化
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initialize)
  } else {
    initialize()
  }

  // 提供全局访问
  return {
    provide: {
      progressiveEnhancement: {
        enhance: initializeEnhancement,
        enhanceArticleIndex,
        enhanceArticleShow,
        enhanceGeneral
      }
    }
  }
})
