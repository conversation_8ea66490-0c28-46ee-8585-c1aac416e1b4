{"name": "skin-tone", "version": "2.0.0", "description": "Change the skin tone of an emoji 👌👌🏻👌🏼👌🏽👌🏾👌🏿", "license": "MIT", "repository": "sindresorhus/skin-tone", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["emoji", "emojis", "skin", "tone", "type", "unicode", "emoticon", "<PERSON><PERSON><PERSON>", "scale", "modify", "change", "strip", "remove"], "dependencies": {"unicode-emoji-modifier-base": "^1.0.0"}, "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}}