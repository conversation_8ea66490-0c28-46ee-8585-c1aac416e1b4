{"name": "unicode-emoji-modifier-base", "version": "1.0.0", "description": "The set of Unicode symbols that can serve as a base for emoji modifiers, i.e. those with the `Emoji_Modifier_Base` property set to `Yes`.", "homepage": "https://github.com/mathiasbynens/unicode-emoji-modifier-base", "main": "index.js", "engines": {"node": ">=4"}, "files": ["LICENSE-MIT.txt", "index.js"], "keywords": ["unicode", "unicode-data", "emoji", "modifier", "Emoji_Modifier_Base"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/unicode-emoji-modifier-base.git"}, "bugs": "https://github.com/mathiasbynens/unicode-emoji-modifier-base/issues", "devDependencies": {"codecov": "^1.0.1", "istanbul": "^0.4.4", "jsesc": "^2.2.0", "lodash.range": "^3.1.5", "mocha": "^2.2.1"}, "scripts": {"download": "curl http://unicode.org/Public/emoji/3.0/emoji-data.txt > data/emoji-data.txt", "build": "node scripts/build.js", "test": "mocha tests", "cover": "istanbul cover --report html node_modules/.bin/_mocha tests -- -u exports -R spec"}}