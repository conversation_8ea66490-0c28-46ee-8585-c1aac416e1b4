{"name": "@nuxtjs/tailwindcss", "version": "7.0.0-beta.0", "description": "Tailwind CSS module for Nuxt", "repository": "nuxt-modules/tailwindcss", "license": "MIT", "type": "module", "configKey": "tailwindcss", "compatibility": {"nuxt": ">=3.0.0"}, "exports": {".": {"types": "./dist/types.d.mts", "import": "./dist/module.mjs"}}, "main": "./dist/module.mjs", "typesVersions": {"*": {".": ["./dist/types.d.mts"]}}, "files": ["dist"], "dependencies": {"@nuxt/kit": "^3.17.1", "@tailwindcss/postcss": "^4.1.6", "@tailwindcss/vite": "^4.1.6", "pathe": "^2.0.3", "tailwindcss": "^4.1.6"}, "devDependencies": {"@nuxt/devtools": "^2.4.0", "@nuxt/eslint": "^1.3.0", "@nuxt/eslint-config": "^1.3.0", "@nuxt/module-builder": "^1.0.1", "@nuxt/test-utils": "^3.17.1", "eslint": "^9.21.0", "happy-dom": "^17.4.7", "nuxt": "^3.17.2", "typescript": "^5.8.3", "vitest": "^3.1.3", "vue-tsc": "^2.2.10"}, "resolutions": {"@nuxtjs/tailwindcss": "link:."}, "stackblitz": {"startCommand": "pnpm dev:prepare && pnpm dev"}, "scripts": {"play": "pnpm dev", "dev": "nuxi dev playground", "dev:build": "nuxi build playground", "dev:generate": "nuxi generate playground", "dev:preview": "nuxi preview playground", "dev:prepare": "nuxt-module-build prepare && pnpm build:stub", "build": "nuxt-module-build build", "build:stub": "pnpm build --stub", "release": "pnpm lint && pnpm test && pnpm prepack && pnpm changelogen --release --push && pnpm publish", "docs:build": "nuxi generate docs", "docs:preview": "nuxi preview docs", "docs:dev": "nuxi dev docs", "lint": "eslint .", "lint:fix": "pnpm lint --fix", "test": "vitest run", "test:watch": "vitest watch", "test:types": "pnpm dev:prepare && tsc --noEmit && nuxi typecheck playground"}}