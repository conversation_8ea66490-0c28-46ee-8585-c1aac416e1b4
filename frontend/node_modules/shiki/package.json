{"name": "shiki", "type": "module", "version": "3.7.0", "description": "A beautiful Syntax Highlighter.", "author": "<PERSON> <<EMAIL>>; <PERSON> <<EMAIL>>", "license": "MIT", "homepage": "https://github.com/shikijs/shiki#readme", "repository": {"type": "git", "url": "git+https://github.com/shikijs/shiki.git", "directory": "packages/shiki"}, "bugs": "https://github.com/shikijs/shiki/issues", "keywords": ["shiki"], "sideEffects": false, "exports": {".": "./dist/index.mjs", "./core": {"unwasm": "./dist/core-unwasm.mjs", "default": "./dist/core.mjs"}, "./wasm": {"unwasm": "./dist/onig.wasm", "default": "./dist/wasm.mjs"}, "./langs": "./dist/langs.mjs", "./themes": "./dist/themes.mjs", "./types": "./dist/types.mjs", "./engine/javascript": "./dist/engine-javascript.mjs", "./engine/oniguruma": "./dist/engine-oniguruma.mjs", "./textmate": "./dist/textmate.mjs", "./bundle/full": "./dist/bundle-full.mjs", "./bundle/web": "./dist/bundle-web.mjs", "./onig.wasm": "./dist/onig.wasm", "./dist/*": "./dist/*", "./package.json": "./package.json", "./*": "./dist/*"}, "main": "./dist/index.mjs", "module": "./dist/index.mjs", "types": "./dist/index.d.mts", "files": ["dist"], "dependencies": {"@shikijs/vscode-textmate": "^10.0.2", "@types/hast": "^3.0.4", "@shikijs/core": "3.7.0", "@shikijs/engine-oniguruma": "3.7.0", "@shikijs/themes": "3.7.0", "@shikijs/langs": "3.7.0", "@shikijs/types": "3.7.0", "@shikijs/engine-javascript": "3.7.0"}, "devDependencies": {"rollup-plugin-copy": "^3.5.0", "tm-grammars": "^1.23.26", "tm-themes": "^1.10.6", "vscode-oniguruma": "1.7.0"}, "scripts": {"build": "unbuild", "dev": "unbuild --stub", "test": "vitest", "test:cf": "wrangler dev test/cf.ts --port 60001"}}