{"name": "vue-component-meta", "version": "2.2.10", "license": "MIT", "files": ["**/*.js", "**/*.d.ts"], "sideEffects": false, "repository": {"type": "git", "url": "https://github.com/vuejs/language-tools.git", "directory": "packages/component-meta"}, "dependencies": {"@volar/typescript": "~2.4.11", "@vue/language-core": "2.2.10", "path-browserify": "^1.0.1", "vue-component-type-helpers": "2.2.10"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "devDependencies": {"@types/node": "^22.10.4", "@types/path-browserify": "^1.0.1"}, "gitHead": "0422c03ffa4958431c9cd3cd19ae51f726c30b07"}