#!/bin/bash

# Lara<PERSON> + Nuxt 渐进式增强项目开发脚本

set -e

echo "🚀 启动 Laravel + Nuxt 开发环境..."

# 检查环境
check_environment() {
    echo "📋 检查开发环境..."
    
    if ! command -v php &> /dev/null; then
        echo "❌ PHP 未安装"
        exit 1
    fi
    
    if ! command -v node &> /dev/null; then
        echo "❌ Node.js 未安装"
        exit 1
    fi
    
    echo "✅ 环境检查通过"
}

# 安装依赖
install_dependencies() {
    echo "📦 安装项目依赖..."
    
    # Laravel 依赖
    if [ ! -d "vendor" ]; then
        echo "安装 Laravel 依赖..."
        composer install
    fi
    
    # Nuxt 依赖
    if [ ! -d "frontend/node_modules" ]; then
        echo "安装 Nuxt 依赖..."
        cd frontend
        npm install
        cd ..
    fi
    
    echo "✅ 依赖安装完成"
}

# 设置环境配置
setup_environment() {
    echo "⚙️  设置环境配置..."
    
    # 复制 .env 文件
    if [ ! -f ".env" ]; then
        cp .env.example .env
        echo "📄 已创建 .env 文件"
    fi
    
    # 生成应用密钥
    if ! grep -q "APP_KEY=base64:" .env; then
        php artisan key:generate
        echo "🔑 已生成应用密钥"
    fi
    
    # 创建数据库文件
    if [ ! -f "database/database.sqlite" ]; then
        touch database/database.sqlite
        echo "🗄️  已创建 SQLite 数据库文件"
    fi
    
    # 运行迁移
    php artisan migrate --force
    echo "📊 数据库迁移完成"
    
    # 运行种子数据
    php artisan db:seed --class=ArticleSeeder --force
    echo "🌱 种子数据填充完成"
    
    echo "✅ 环境配置完成"
}

# 启动开发服务器
start_servers() {
    echo "🌐 启动开发服务器..."
    
    # 创建日志目录
    mkdir -p logs
    
    # 启动 Laravel 开发服务器
    echo "🚀 启动 Laravel 服务器 (http://localhost:8000)..."
    php artisan serve --host=0.0.0.0 --port=8000 > logs/laravel.log 2>&1 &
    LARAVEL_PID=$!
    
    # 等待 Laravel 启动
    sleep 3
    
    # 启动 Nuxt 开发服务器
    echo "🚀 启动 Nuxt 开发服务器 (http://localhost:3001)..."
    cd frontend
    npm run dev > ../logs/nuxt.log 2>&1 &
    NUXT_PID=$!
    cd ..
    
    # 等待服务器启动
    sleep 5
    
    echo ""
    echo "🎉 开发环境启动成功！"
    echo ""
    echo "📋 服务信息:"
    echo "   🌐 Laravel 应用: http://localhost:8000"
    echo "   🎨 Nuxt 开发服务器: http://localhost:3001"
    echo "   📊 数据库: SQLite (database/database.sqlite)"
    echo ""
    echo "📖 开发说明:"
    echo "   - Laravel 处理路由和静态内容渲染"
    echo "   - Nuxt 提供客户端渐进式增强"
    echo "   - 修改 Blade 模板会自动刷新"
    echo "   - 修改 Vue 组件会热重载"
    echo ""
    echo "📁 重要文件:"
    echo "   - Laravel 路由: routes/web.php"
    echo "   - Blade 模板: resources/views/"
    echo "   - Vue 组件: frontend/components/"
    echo "   - Nuxt 配置: frontend/nuxt.config.ts"
    echo ""
    echo "🔧 开发工具:"
    echo "   - Laravel 日志: tail -f logs/laravel.log"
    echo "   - Nuxt 日志: tail -f logs/nuxt.log"
    echo "   - 数据库管理: php artisan tinker"
    echo ""
    echo "⏹️  停止服务: Ctrl+C 或运行 ./stop-dev.sh"
    echo ""
    
    # 保存进程 ID
    echo $LARAVEL_PID > logs/laravel.pid
    echo $NUXT_PID > logs/nuxt.pid
    
    # 等待用户中断
    trap cleanup INT TERM
    
    echo "🔄 服务运行中... (按 Ctrl+C 停止)"
    wait
}

# 清理函数
cleanup() {
    echo ""
    echo "🛑 正在停止开发服务器..."
    
    # 停止 Laravel
    if [ -f "logs/laravel.pid" ]; then
        LARAVEL_PID=$(cat logs/laravel.pid)
        if kill -0 $LARAVEL_PID 2>/dev/null; then
            kill $LARAVEL_PID
            echo "✅ Laravel 服务器已停止"
        fi
        rm -f logs/laravel.pid
    fi
    
    # 停止 Nuxt
    if [ -f "logs/nuxt.pid" ]; then
        NUXT_PID=$(cat logs/nuxt.pid)
        if kill -0 $NUXT_PID 2>/dev/null; then
            kill $NUXT_PID
            echo "✅ Nuxt 服务器已停止"
        fi
        rm -f logs/nuxt.pid
    fi
    
    echo "👋 开发环境已停止"
    exit 0
}

# 主函数
main() {
    check_environment
    install_dependencies
    setup_environment
    start_servers
}

# 运行主流程
main "$@"
